<template>
  <div>
    <!-- Step 3. Export Result -->
    <el-divider class="el-divider--nowrap">Step 3. Export Result</el-divider>
    <div v-if="results.length" class="download-section">
      <el-collapse expand-icon-position="right">
        <el-collapse-item title="Tag Formatting Options">
          <el-row :gutter="20" justify="center" class="center-content">
            <!-- Prefix or suffix for new customized tags -->
            <el-col :span="6" :xs="24">
              <el-checkbox v-model="newTagMarkEnabled">Add <el-select v-model="newTagMarkPosition"
                  :disabled="!newTagMarkEnabled" size="small" class="mark-position-select">
                  <el-option label="prefix" value="prefix"></el-option>
                  <el-option label="suffix" value="suffix"></el-option>
                </el-select>
                to customized tags</el-checkbox>
              <el-input :disabled="!newTagMarkEnabled" v-model="newTagMark" placeholder="Enter prefix">
                <template #prepend>
                  <el-tooltip
                    content="This prefix will be added to all customized tags (new tags not in the tag pool) in the exported CSV"
                    placement="top">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>
              <!-- Metadata suffix section -->
              <el-checkbox v-model="metadataSuffixEnabled">
                Add metadata suffix to matched tags
              </el-checkbox>
              <el-select :disabled="!metadataSuffixEnabled || !(availableMetadataFields.length > 0)"
                v-model="selectedMetadataFields" multiple placeholder="Select metadata fields">
                <el-option v-for="field in availableMetadataFields" :key="field.value" :label="field.label"
                  :value="field.value" />
              </el-select>
            </el-col>

            <el-col :span="6" :xs="24">
              <!-- Custom suffix section -->
              <el-checkbox v-model="customSuffixEnabled">
                Add custom suffix
              </el-checkbox>
              <el-input v-model="customSuffix" placeholder="Enter custom suffix" :disabled="!customSuffixEnabled"
                class="suffix-input">
                <template #prepend>
                  <el-tooltip content="This suffix will be added to all matched tags when saving to Zotero"
                    placement="top">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>
              <!-- "Processed by this tool" tag -->
              <el-checkbox v-model="processedTagEnabled">
                Add an extra tag for all processed items
              </el-checkbox>
              <el-input v-model="processedTag" placeholder="Enter marker tag" :disabled="!processedTagEnabled">
                <template #prepend>
                  <el-tooltip content="This tag will be added to all items processed by this tool" placement="top">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-col>

            <el-col :span="6" :xs="24" class="center-content">
              <!-- Preview section -->
              <div><el-text size="default">Preview of tags to be added: </el-text></div>
              <el-tag v-if="!(metadataSuffixEnabled && selectedMetadataFields.length > 0) && !customSuffixEnabled"
                type="primary">
                matched-tags
              </el-tag>
              <el-tag v-if="!newTagMarkEnabled && !customSuffixEnabled" type="warning">
                customized-tags
              </el-tag>

              <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && customSuffixEnabled"
                class="preview-tag" type="primary">
                matched-tags{{ getPreviewSuffix() }}{{ customSuffix }}
              </el-tag>
              <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && !customSuffixEnabled"
                class="preview-tag" type="primary">
                matched-tags{{ getPreviewSuffix() }}
              </el-tag>
              <el-tag v-if="!metadataSuffixEnabled && customSuffixEnabled" type="primary">
                matched-tags{{ customSuffix }}
              </el-tag>

              <el-tag v-if="!newTagMarkEnabled && customSuffixEnabled" type="warning">
                customized-tags{{ customSuffix }}
              </el-tag>
              <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && customSuffixEnabled" type="warning">
                {{ newTagMark }}customized-tags{{ customSuffix }}
              </el-tag>
              <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && customSuffixEnabled" type="warning">
                customized-tags{{ newTagMark }}{{ customSuffix }}
              </el-tag>
              <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && !customSuffixEnabled"
                type="warning">
                {{ newTagMark }}customized-tags
              </el-tag>
              <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && !customSuffixEnabled"
                type="warning">
                customized-tags{{ newTagMark }}
              </el-tag>

              <el-tag v-if="processedTagEnabled" type="info">
                {{ processedTag }}
              </el-tag>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>

      <el-row :gutter="10" justify="center" class="mt-4 center-content">
        <!-- Consolidated export controls -->
        <el-col :span="12" :xs="24">
          <el-row :gutter="10" justify="center" class="center-content">
            <el-col :span="5" :xs="24">
              <!-- Download button -->
              <el-button type="success" @click="handleDownload" :disabled="!selectedExportFormat">
                Download As
              </el-button>
            </el-col>
            <el-col :span="4" :xs="8">
              <!-- Format selection dropdown -->
              <el-select v-model="selectedExportFormat" placeholder="Select format">
                <el-option label="RDF" value="rdf"></el-option>
                <el-option label="RIS" value="ris"></el-option>
                <el-option label="BibTeX" value="bibtex"></el-option>
                <el-option label="CSV" value="csv"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4" :xs="24">
              <!-- File Options button -->
              <el-button type="info" @click="handleOpenOptionsDialog"
                :disabled="results.length === 0 || !selectedExportFormat">
                Options
              </el-button>
            </el-col>
          </el-row>

          <!-- Dialog for CSV export options -->
          <el-dialog v-model="showExportDialog" title="CSV Export Options" :width="screenIsPortrait ? '90%' : '50%'">
            <el-form :model="exportOptions">
              <el-divider class="el-divider--nowrap">Fields to export</el-divider>
              <el-checkbox v-model="selectAll" @change="handleSelectAll">Select All</el-checkbox>
              <el-row :gutter="5" justify="center">
                <el-checkbox-group v-model="exportFields">
                  <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                    {{ field }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-row>
              <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
              <el-row :gutter="10" justify="center">
                <el-checkbox v-model="includeConceptTags">Concept tags</el-checkbox>
                <el-checkbox v-model="includePersonOrgTags">Person/Organization tags</el-checkbox>
                <el-checkbox v-model="includeTimePlaceTags">Time/Place tags</el-checkbox>
              </el-row>

              <el-divider class="el-divider--nowrap">Delimiter settings</el-divider>
              <el-row :gutter="10" justify="center" class="center-content">
                <el-col :span="6" :xs="24">
                  <el-text>Field delimiter: </el-text>
                  <el-input v-model="exportOptions.fieldDelimiter" class="el-input-delimiter"></el-input>
                </el-col>
                <el-col :span="6" :xs="24">
                  <el-text>Tag delimiter: </el-text>
                  <el-input v-model="exportOptions.tagDelimiter" class="el-input-delimiter"></el-input>
                </el-col>
                <el-col :span="6" :xs="24">
                  <el-text>Field enclosure: </el-text>
                  <el-input v-model="exportOptions.valueMarkers" class="el-input-delimiter"></el-input>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showExportDialog = false">Cancel</el-button>
                <el-button type="success" @click="handleExport">Download To CSV</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- BibTeX Export Dialog -->
          <el-dialog v-model="showBibTeXDialog" title="BibTeX Export Options" :width="screenIsPortrait ? '90%' : '50%'">
            <el-form :model="bibTeXOptions">
              <el-divider class="el-divider--nowrap">Fields to export</el-divider>
              <el-checkbox v-model="selectAllBibTeX" @change="handleSelectAllBibTeX">Select All</el-checkbox>
              <el-row :gutter="5" justify="center">
                <el-checkbox-group v-model="bibTeXFields">
                  <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                    {{ field }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-row>
              <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
              <el-row :gutter="10" justify="center">
                <el-checkbox v-model="includeConceptTagsBibTeX">Concept tags</el-checkbox>
                <el-checkbox v-model="includePersonOrgTagsBibTeX">Person/Organization tags</el-checkbox>
                <el-checkbox v-model="includeTimePlaceTagsBibTeX">Time/Place tags</el-checkbox>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showBibTeXDialog = false">Cancel</el-button>
                <el-button type="success" @click="handleBibTeXExport">Download To BibTeX</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- RIS Export Dialog -->
          <el-dialog v-model="showRISDialog" title="RIS Export Options" :width="screenIsPortrait ? '90%' : '50%'">
            <el-form :model="risOptions">
              <el-divider class="el-divider--nowrap">Fields to export</el-divider>
              <el-checkbox v-model="selectAllRIS" @change="handleSelectAllRIS">Select All</el-checkbox>
              <el-row :gutter="5" justify="center">
                <el-checkbox-group v-model="risFields">
                  <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                    {{ field }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-row>
              <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
              <el-row :gutter="10" justify="center">
                <el-checkbox v-model="includeConceptTagsRIS">Concept tags</el-checkbox>
                <el-checkbox v-model="includePersonOrgTagsRIS">Person/Organization tags</el-checkbox>
                <el-checkbox v-model="includeTimePlaceTagsRIS">Time/Place tags</el-checkbox>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showRISDialog = false">Cancel</el-button>
                <el-button type="success" @click="handleRISExport">Download To RIS</el-button>
              </span>
            </template>
          </el-dialog>

          <!-- Zotero RDF Export Dialog -->
          <el-dialog v-model="showRDFDialog" title="Zotero RDF Export Options"
            :width="screenIsPortrait ? '90%' : '50%'">
            <el-form :model="rdfOptions">
              <!-- Show original file update option only for Zotero RDF files -->
              <div v-if="rdfData && rdfData.isZoteroRdf">
                <el-divider class="el-divider--nowrap">Export Mode</el-divider>
                <el-row justify="center">
                  <el-checkbox v-model="updateOriginalZoteroFile">
                    Update original Zotero RDF file with active tags
                    <el-tooltip effect="dark" placement="right">
                      <template #content>
                        When enabled, active tags will be added to the original imported file<br />
                        as &lt;dc:subject&gt; elements, preserving the original file structure.<br />An updated copy of the original file will be saved.
                      </template>
                      <el-icon style="margin-left: 4px;">
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </el-checkbox>
                </el-row>

                <!-- Tag removal options - only show when updating original file -->
                <div v-if="updateOriginalZoteroFile">
                  <el-divider class="el-divider--nowrap">Removal of Specified Tags</el-divider>
                  <el-row justify="center" style="margin-bottom: 16px;">
                    <el-checkbox v-model="enableTagRemoval">
                      Remove the following tags from original RDF file
                    </el-checkbox>
                  </el-row>
                  <el-row v-if="enableTagRemoval" justify="center">
                    <el-col :span="12">
                      <el-form-item>
                        <el-input-tag
                          v-model="tagsToRemove"
                          clearable
                          placeholder="Type and press Enter to add tags"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <div v-if="!updateOriginalZoteroFile">
                <el-divider class="el-divider--nowrap">Fields to export</el-divider>
                <el-checkbox v-model="selectAllRDF" @change="handleSelectAllRDF">Select All</el-checkbox>
                <el-row :gutter="5" justify="center">
                  <el-checkbox-group v-model="rdfFields">
                    <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                      {{ field }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-row>
              </div>
              <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
              <el-row :gutter="10" justify="center">
                <el-checkbox v-model="includeConceptTagsRDF">Concept tags</el-checkbox>
                <el-checkbox v-model="includePersonOrgTagsRDF">Person/Organization tags</el-checkbox>
                <el-checkbox v-model="includeTimePlaceTagsRDF">Time/Place tags</el-checkbox>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showRDFDialog = false">Cancel</el-button>
                <el-button type="success" @click="handleRDFExport">
                  {{ updateOriginalZoteroFile ? 'Download To Zotero RDF' : 'Download To RDF' }}
                </el-button>
              </span>
            </template>
          </el-dialog>
        </el-col>
        <!-- Save to Zotero button -->
        <el-col :span="6" :xs="24">
          <el-tooltip :disabled="currentSource === 'Zotero'"
            content="Please import items from Zotero to enable this option." placement="top">
            <el-button type="success" @click="handleSaveTagsToZotero" :disabled="currentSource !== 'Zotero'">
              Save Matched Tags to Zotero
            </el-button>
          </el-tooltip>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, toRaw, isReactive } from 'vue'
import axios from 'axios'
import {
  ElDivider,
  ElRow,
  ElCol,
  ElCheckbox,
  ElSelect,
  ElOption,
  ElInput,
  ElTooltip,
  ElIcon,
  ElText,
  ElTag,
  ElButton,
  ElDialog,
  ElForm,
  ElCheckboxGroup,
  ElMessage
} from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

// Props - now only receiving data needed from parent
const props = defineProps({
  results: {
    type: Array,
    required: true
  },
  allIndexedBiblioItems: {
    type: Array,
    required: true
  },
  allTagCandidates: {
    type: Array,
    required: true
  },
  deselectedTags: {
    type: Set,
    required: true
  },
  newTags: {
    type: Map,
    required: true
  },
  currentSource: {
    type: String,
    required: true
  },
  zoteroConfig: {
    type: Object,
    default: null
  },
  zoteroBaseUrl: {
    type: String,
    default: ''
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  apiAllTagsUrl: {
    type: String,
    required: true
  },
  isFetchingTags: {
    type: Boolean,
    required: true
  },
  hasLoadedTags: {
    type: Boolean,
    required: true
  },
  rdfData: {
    type: Object,
    default: null
  }
})

// Internal state - moved from App.vue
const newTagMarkEnabled = ref(true)
const newTagMarkPosition = ref('suffix')
const newTagMark = ref('[NEW]')
const metadataSuffixEnabled = ref(true)
const selectedMetadataFields = ref(['record_id'])
const customSuffixEnabled = ref(false)
const customSuffix = ref('[IsisCBtag]')
const processedTagEnabled = ref(true)
const processedTag = ref('processed-by-tagger')
const exportFields = ref([])
const includeConceptTags = ref(false)
const includePersonOrgTags = ref(false)
const includeTimePlaceTags = ref(false)
const showExportDialog = ref(false)
const selectAll = ref(false)
const availableMetadataFields = ref([])

// BibTeX export state
const bibTeXFields = ref([])
const includeConceptTagsBibTeX = ref(false)
const includePersonOrgTagsBibTeX = ref(false)
const includeTimePlaceTagsBibTeX = ref(false)
const showBibTeXDialog = ref(false)
const selectAllBibTeX = ref(false)

// RIS export state
const risFields = ref([])
const includeConceptTagsRIS = ref(false)
const includePersonOrgTagsRIS = ref(false)
const includeTimePlaceTagsRIS = ref(false)
const showRISDialog = ref(false)
const selectAllRIS = ref(false)

// RDF export state
const rdfFields = ref([])
const includeConceptTagsRDF = ref(false)
const includePersonOrgTagsRDF = ref(false)
const includeTimePlaceTagsRDF = ref(false)
const showRDFDialog = ref(false)
const selectAllRDF = ref(false)
const updateOriginalZoteroFile = ref(false)
const enableTagRemoval = ref(false)
const tagsToRemove = ref(['to-be-tagged'])

// New consolidated export state
const selectedExportFormat = ref('rdf') // Default to Zotero RDF because it has better compatibility with Zotero

const exportOptions = ref({
  fieldDelimiter: ',',
  tagDelimiter: '#',
  valueMarkers: "'''"
})

const bibTeXOptions = ref({
  tagDelimiter: ', '
})

const risOptions = ref({
  tagDelimiter: ', '
})

const rdfOptions = ref({
  tagDelimiter: ', '
})

// Emits - simplified to only emit events that parent needs to know about
const emit = defineEmits([
  'fetchAllTags' // Request parent to fetch tags when needed
])

// Computed properties
const availableFields = computed(() => {
  // 1. Handle empty array
  if (props.allIndexedBiblioItems.length === 0) return []

  // 2. Get all keys from ALL items and create a unique set
  const allKeys = [...new Set(props.allIndexedBiblioItems.flatMap(item => Object.keys(item)))]

  // 3. Filter this complete list of keys
  return allKeys
    .filter(key => key !== 'index') // Exclude the 'index' key
    .filter(key =>               // Keep the key if AT LEAST ONE item has a non-empty value for it
      props.allIndexedBiblioItems.some(item => {
        const value = item[key]
        return value !== null && value !== undefined && String(value).trim() !== ''
      })
    )
})

// Business logic functions moved from App.vue
const updateMetadataFields = () => {
  if (!props.allTagCandidates || props.allTagCandidates.length === 0) {
    availableMetadataFields.value = [{ label: 'N/A', value: 'N/A' }]
    return
  }

  const firstObject = props.allTagCandidates[0]
  const fields = Object.keys(toRaw(firstObject || {}))
  const filteredFields = fields.filter(field => field !== 'name')

  availableMetadataFields.value = filteredFields.map(field => ({
    label: field,
    value: field
  }))
}

const getPreviewSuffix = () => {
  let suffix = ''

  if (metadataSuffixEnabled.value && selectedMetadataFields.value.length > 0) {
    suffix += `[${selectedMetadataFields.value.join('|')}]`
  }

  return suffix
}

const isTagDeselected = (resultIndex, tag) => {
  return props.deselectedTags.has(`${resultIndex}-${tag}`)
}

const openExportDialog = () => {
  exportFields.value = [...availableFields.value]
  selectAll.value = true
  showExportDialog.value = true
}

const handleSelectAll = (val) => {
  if (val) {
    exportFields.value = [...availableFields.value]
  } else {
    exportFields.value = []
  }
}

const handleSelectAllBibTeX = (val) => {
  if (val) {
    bibTeXFields.value = [...availableFields.value]
  } else {
    bibTeXFields.value = []
  }
}

const handleSelectAllRIS = (val) => {
  if (val) {
    risFields.value = [...availableFields.value]
  } else {
    risFields.value = []
  }
}

const handleSelectAllRDF = (val) => {
  if (val) {
    rdfFields.value = [...availableFields.value]
  } else {
    rdfFields.value = []
  }
}

const handleExport = () => {
  downloadCSV()
  showExportDialog.value = false
}

const openBibTeXDialog = () => {
  bibTeXFields.value = [...availableFields.value]
  selectAllBibTeX.value = true
  showBibTeXDialog.value = true
}

const handleBibTeXExport = () => {
  downloadBibTeX()
  showBibTeXDialog.value = false
}

const openRISDialog = () => {
  risFields.value = [...availableFields.value]
  selectAllRIS.value = true
  showRISDialog.value = true
}

const handleRISExport = () => {
  downloadRIS()
  showRISDialog.value = false
}

const openRDFDialog = () => {
  rdfFields.value = [...availableFields.value]
  selectAllRDF.value = true
  // Set default value for update original file option based on whether it's a Zotero RDF file
  updateOriginalZoteroFile.value = props.rdfData && props.rdfData.isZoteroRdf
  // Auto-enable tag removal when updating original Zotero file
  enableTagRemoval.value = updateOriginalZoteroFile.value
  // Reset tags to remove to default value
  tagsToRemove.value = ['to-be-tagged']
  showRDFDialog.value = true
}

const handleRDFExport = () => {
  downloadRDF()
  showRDFDialog.value = false
}

// Watch for changes to updateOriginalZoteroFile to auto-enable tag removal
watch(updateOriginalZoteroFile, (newValue) => {
  if (newValue) {
    enableTagRemoval.value = true
  }
})

const downloadCSV = () => {
  // If the user directly clicked the export button, the exportFields array would be empty. So it should be set to all available fields by default.
  if (exportFields.value.length === 0) {
    exportFields.value = [...availableFields.value]
  }

  const { fieldDelimiter, tagDelimiter, valueMarkers } = exportOptions.value
  const startMarker = valueMarkers || ''
  const endMarker = valueMarkers || startMarker

  const csvData = props.results.map(result => {
    const item = props.allIndexedBiblioItems.find(item => item.index === result.index)
    const row = exportFields.value.map(field => {
      // Replace undefined or null field values with an empty string
      let fieldValue = item[field] != null ? item[field] : ''

      // If fieldValue is not a string and is an object or array (e.g. a list of several authors), expand it
      if (isReactive(fieldValue) || Array.isArray(fieldValue)) {
        const rawFieldValue = toRaw(fieldValue) // Convert to raw object/array

        if (Array.isArray(rawFieldValue)) {
          // Check for array of objects with firstName or lastName
          fieldValue = rawFieldValue
            .map(entry => {
              if (entry.lastName || entry.firstName) {
                // Use empty string if one of the values is missing
                const lastName = entry.lastName || ''
                const firstName = entry.firstName || ''
                return `${lastName},${firstName}`.trim()
              }
              return '' // Skip invalid entries
            })
            .filter(Boolean) // Remove empty strings
            .join(tagDelimiter) // Join with tagDelimiter
        }
      } else if (typeof fieldValue === 'object') {
        // Handle plain objects by joining values with tagDelimiter
        fieldValue = Object.values(fieldValue).join(tagDelimiter)
      } else if (typeof fieldValue !== 'string') {
        // Convert other non-string values to strings
        fieldValue = String(fieldValue)
      }
      return `${startMarker}${fieldValue}${endMarker}`
    })

    // Decorate active matched tags (those matched tags that are still active (not de-selected) after being edited by the user)
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = props.allTagCandidates.find(t => t.name === tagText)

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = tagText
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`
        }

        return decoratedTagText
      })

    // Process newly added customized tags (tags added by user and not in the tag pool)
    const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`
          }
        }

        const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`
        }

        return decoratedNewTagText
      })

    // Combine all tags and add processed tag if enabled
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value)
    }

    // Add combined tags to row
    row.push(`${startMarker}${allDecoratedTags.join(tagDelimiter)}${endMarker}`)

    // Add other tag categories if selected
    if (includeConceptTags.value) row.push(`${startMarker}${result.tags.concept_tags.join(tagDelimiter)}${endMarker}`)
    if (includePersonOrgTags.value) row.push(`${startMarker}${result.tags.person_org_tags.join(tagDelimiter)}${endMarker}`)
    if (includeTimePlaceTags.value) row.push(`${startMarker}${result.tags.time_place_tags.join(tagDelimiter)}${endMarker}`)

    return row.join(fieldDelimiter)
  })

  // Create header row
  const headerRow = [
    ...exportFields.value,
    'Matched Tags',
    ...(includeConceptTags.value ? ['Concept Tags'] : []),
    ...(includePersonOrgTags.value ? ['Person/Org Tags'] : []),
    ...(includeTimePlaceTags.value ? ['Time/Place Tags'] : [])
  ].map(header => `${startMarker}${header}${endMarker}`)

  // Add header row to csvData
  csvData.unshift(headerRow.join(fieldDelimiter))

  const csv = csvData.join('\n')
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'tagged_items.csv'
  link.click()
}

// BibTeX field mapping function
const mapToBibTeXField = (fieldName, value) => {
  // Handle array values (like authors)
  if (Array.isArray(value)) {
    if (fieldName === 'creators' || fieldName === 'author') {
      return value.map(creator => {
        if (creator.lastName && creator.firstName) {
          return `${creator.lastName}, ${creator.firstName}`
        } else if (creator.lastName) {
          return creator.lastName
        } else if (creator.firstName) {
          return creator.firstName
        }
        return String(creator)
      }).join(' and ')
    }
    return value.join(' and ')
  }

  // Return string value
  return String(value || '')
}

// Generate BibTeX citation key
const generateCitationKey = (item) => {
  let key = ''

  // Try to use first author's last name
  if (item.creators && Array.isArray(item.creators) && item.creators.length > 0) {
    const firstAuthor = item.creators[0]
    if (firstAuthor.lastName) {
      key += firstAuthor.lastName.replace(/[^a-zA-Z0-9]/g, '')
    }
  } else if (item.author) {
    // Handle author as string
    const authorMatch = String(item.author).match(/([^,\s]+)/)
    if (authorMatch) {
      key += authorMatch[1].replace(/[^a-zA-Z0-9]/g, '')
    }
  }

  // Add year if available
  if (item.year || item.date) {
    const year = item.year || item.date
    const yearMatch = String(year).match(/(\d{4})/)
    if (yearMatch) {
      key += yearMatch[1]
    }
  }

  // Add title words if key is still short
  if (key.length < 5 && item.title) {
    const titleWords = String(item.title)
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .slice(0, 3)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    key += titleWords.join('')
  }

  // Fallback to generic key if still empty
  if (!key) {
    key = 'Item'
  }

  return key
}

// Download BibTeX function
const downloadBibTeX = () => {
  // If the user directly clicked the export button, the bibTeXFields array would be empty. So it should be set to all available fields by default.
  if (bibTeXFields.value.length === 0) {
    bibTeXFields.value = [...availableFields.value]
  }

  const { tagDelimiter } = bibTeXOptions.value
  const usedKeys = new Set()

  const bibTeXEntries = props.results.map(result => {
    const item = props.allIndexedBiblioItems.find(item => item.index === result.index)

    // Generate unique citation key
    let citationKey = generateCitationKey(item)
    let counter = 1
    while (usedKeys.has(citationKey)) {
      citationKey = generateCitationKey(item) + counter
      counter++
    }
    usedKeys.add(citationKey)

    // Determine entry type based on available fields
    let entryType = 'misc' // default
    if (item.itemType) {
      switch (item.itemType.toLowerCase()) {
        case 'journalarticle':
          entryType = 'article'
          break
        case 'book':
          entryType = 'book'
          break
        case 'bookchapter':
        case 'booksection':
          entryType = 'inbook'
          break
        case 'conferencepaper':
          entryType = 'inproceedings'
          break
        case 'thesis':
          entryType = 'phdthesis'
          break
        case 'report':
          entryType = 'techreport'
          break
        default:
          entryType = 'misc'
      }
    } else if (item.publicationTitle || item.journal) {
      entryType = 'article'
    } else if (item.publisher) {
      entryType = 'book'
    }

    // Build BibTeX fields
    const bibFields = []

    // Process selected fields
    bibTeXFields.value.forEach(field => {
      if (item[field] != null && String(item[field]).trim() !== '') {
        let bibFieldName = field
        let bibFieldValue = item[field]

        // Map common field names to BibTeX standard names
        switch (field.toLowerCase()) {
          case 'publicationtitle':
          case 'journal':
            bibFieldName = 'journal'
            break
          case 'creators':
          case 'author':
            bibFieldName = 'author'
            bibFieldValue = mapToBibTeXField(field, bibFieldValue)
            break
          case 'date':
            bibFieldName = 'year'
            // Extract year from date
            const yearMatch = String(bibFieldValue).match(/(\d{4})/)
            bibFieldValue = yearMatch ? yearMatch[1] : bibFieldValue
            break
          case 'abstractnote':
            bibFieldName = 'abstract'
            break
          case 'itemtype':
            // Skip itemType as it's used for entry type determination
            return
          case 'key':
          case 'version':
            // Skip internal fields
            return
          default:
            // Keep original field name for other fields
            break
        }

        // Format field value
        if (typeof bibFieldValue !== 'string') {
          bibFieldValue = mapToBibTeXField(field, bibFieldValue)
        }

        // Escape special characters and wrap in braces
        bibFieldValue = String(bibFieldValue)
          .replace(/[{}]/g, '') // Remove existing braces
          .replace(/([A-Z][a-z]+)/g, '{$1}') // Protect capitalized words

        bibFields.push(`\t${bibFieldName} = {${bibFieldValue}}`)
      }
    })

    // Process active tags and add to keywords field
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = props.allTagCandidates.find(t => t.name === tagText)

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = tagText
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`
        }

        return decoratedTagText
      })

    // Process newly added customized tags
    const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`
          }
        }

        const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`
        }

        return decoratedNewTagText
      })

    // Combine all tags
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]

    // Add other tag categories if selected
    if (includeConceptTagsBibTeX.value) allDecoratedTags.push(...result.tags.concept_tags)
    if (includePersonOrgTagsBibTeX.value) allDecoratedTags.push(...result.tags.person_org_tags)
    if (includeTimePlaceTagsBibTeX.value) allDecoratedTags.push(...result.tags.time_place_tags)

    // Add processed tag if enabled
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value)
    }

    // Add keywords field if there are tags
    if (allDecoratedTags.length > 0) {
      const keywordsValue = allDecoratedTags.join(tagDelimiter)
      bibFields.push(`\tkeywords = {${keywordsValue}}`)
    }

    // Build the complete BibTeX entry
    return `@${entryType}{${citationKey},\n${bibFields.join(',\n')}\n}`
  })

  // Create the complete BibTeX content
  const bibTeXContent = bibTeXEntries.join('\n\n')

  // Download the file
  const blob = new Blob([bibTeXContent], { type: 'text/plain;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'tagged_items.bib'
  link.click()
}

// RIS field mapping function
const mapToRISField = (fieldName, value) => {
  // Handle array values (like authors)
  if (Array.isArray(value)) {
    if (fieldName === 'creators' || fieldName === 'author') {
      return value.map(creator => {
        if (creator.lastName && creator.firstName) {
          return `${creator.lastName}, ${creator.firstName}`
        } else if (creator.lastName) {
          return creator.lastName
        } else if (creator.firstName) {
          return creator.firstName
        }
        return String(creator)
      })
    }
    return value.map(v => String(v))
  }

  // Return string value
  return String(value || '')
}

// Download RIS function
const downloadRIS = () => {
  // If the user directly clicked the export button, the risFields array would be empty. So it should be set to all available fields by default.
  if (risFields.value.length === 0) {
    risFields.value = [...availableFields.value]
  }

  const { tagDelimiter } = risOptions.value

  const risEntries = props.results.map(result => {
    const item = props.allIndexedBiblioItems.find(item => item.index === result.index)

    // Determine RIS type based on available fields
    let risType = 'GEN' // Generic - default
    if (item.itemType) {
      switch (item.itemType.toLowerCase()) {
        case 'journalarticle':
          risType = 'JOUR'
          break
        case 'book':
          risType = 'BOOK'
          break
        case 'bookchapter':
        case 'booksection':
          risType = 'CHAP'
          break
        case 'conferencepaper':
          risType = 'CONF'
          break
        case 'thesis':
          risType = 'THES'
          break
        case 'report':
          risType = 'RPRT'
          break
        case 'webpage':
          risType = 'ELEC'
          break
        case 'newspaper':
          risType = 'NEWS'
          break
        case 'magazine':
          risType = 'MGZN'
          break
        default:
          risType = 'GEN'
      }
    } else if (item.publicationTitle || item.journal) {
      risType = 'JOUR'
    } else if (item.publisher) {
      risType = 'BOOK'
    }

    // Build RIS fields
    const risFieldsArray = []

    // Start with type
    risFieldsArray.push(`TY  - ${risType}`)

    // Process selected fields
    risFields.value.forEach(field => {
      if (item[field] != null && String(item[field]).trim() !== '') {
        let risFieldName = ''
        let risFieldValue = item[field]

        // Map common field names to RIS standard tags
        switch (field.toLowerCase()) {
          case 'title':
            risFieldName = 'TI'
            break
          case 'creators':
          case 'author':
            risFieldName = 'AU'
            const authors = mapToRISField(field, risFieldValue)
            if (Array.isArray(authors)) {
              authors.forEach(author => {
                risFieldsArray.push(`AU  - ${author}`)
              })
              return // Skip the general processing below
            }
            break
          case 'publicationtitle':
          case 'journal':
            risFieldName = 'JO'
            break
          case 'date':
          case 'year':
            risFieldName = 'PY'
            // Extract year from date
            const yearMatch = String(risFieldValue).match(/(\d{4})/)
            risFieldValue = yearMatch ? yearMatch[1] : risFieldValue
            break
          case 'abstract':
          case 'abstractnote':
            risFieldName = 'AB'
            break
          case 'volume':
            risFieldName = 'VL'
            break
          case 'issue':
            risFieldName = 'IS'
            break
          case 'pages':
            risFieldName = 'SP'
            // Handle page ranges
            const pageMatch = String(risFieldValue).match(/(\d+)(?:-+(\d+))?/)
            if (pageMatch) {
              risFieldsArray.push(`SP  - ${pageMatch[1]}`)
              if (pageMatch[2]) {
                risFieldsArray.push(`EP  - ${pageMatch[2]}`)
              }
              return
            }
            break
          case 'publisher':
            risFieldName = 'PB'
            break
          case 'place':
            risFieldName = 'CY'
            break
          case 'url':
            risFieldName = 'UR'
            break
          case 'doi':
            risFieldName = 'DO'
            break
          case 'isbn':
            risFieldName = 'SN'
            break
          case 'issn':
            risFieldName = 'SN'
            break
          case 'language':
            risFieldName = 'LA'
            break
          case 'itemtype':
          case 'key':
          case 'version':
            // Skip internal fields
            return
          default:
            // Use custom fields for unmapped fields
            risFieldName = 'N1'
            risFieldValue = `${field}: ${risFieldValue}`
            break
        }

        // Format field value
        if (typeof risFieldValue !== 'string') {
          risFieldValue = mapToRISField(field, risFieldValue)
          if (Array.isArray(risFieldValue)) {
            risFieldValue = risFieldValue.join('; ')
          }
        }

        // Add field if we have a mapping
        if (risFieldName) {
          risFieldsArray.push(`${risFieldName}  - ${String(risFieldValue)}`)
        }
      }
    })

    // Process active tags and add to keywords field
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = props.allTagCandidates.find(t => t.name === tagText)

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = tagText
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`
        }

        return decoratedTagText
      })

    // Process newly added customized tags
    const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`
          }
        }

        const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`
        }

        return decoratedNewTagText
      })

    // Combine all tags
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]

    // Add other tag categories if selected
    if (includeConceptTagsRIS.value) allDecoratedTags.push(...result.tags.concept_tags)
    if (includePersonOrgTagsRIS.value) allDecoratedTags.push(...result.tags.person_org_tags)
    if (includeTimePlaceTagsRIS.value) allDecoratedTags.push(...result.tags.time_place_tags)

    // Add processed tag if enabled
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value)
    }

    // Add keywords fields if there are tags
    if (allDecoratedTags.length > 0) {
      allDecoratedTags.forEach(tag => {
        risFieldsArray.push(`KW  - ${tag}`)
      })
    }

    // End record
    risFieldsArray.push('ER  - ')

    // Build the complete RIS entry
    return risFieldsArray.join('\n')
  })

  // Create the complete RIS content
  const risContent = risEntries.join('\n\n')

  // Download the file
  const blob = new Blob([risContent], { type: 'text/plain;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'tagged_items.ris'
  link.click()
}

// Function to update original Zotero RDF file with active tags
const updateOriginalRDFFile = () => {
  if (!props.rdfData || !props.rdfData.originalContent) {
    ElMessage.error('Original RDF content not available')
    return
  }

  let updatedRdfContent = props.rdfData.originalContent

  // Remove specified tags from the RDF content if tag removal is enabled
  if (enableTagRemoval.value && tagsToRemove.value.length > 0) {
    tagsToRemove.value.forEach(tagToRemove => {
      if (tagToRemove.trim()) {
        // Create regex to match dc:subject elements containing the tag to remove
        // This handles both self-closing and regular tags, with proper XML escaping
        const escapedTag = tagToRemove.trim()
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')

        // Match both formats: <dc:subject>tag</dc:subject> and variations with whitespace
        const tagRegex = new RegExp(`\\s*<dc:subject[^>]*>\\s*${escapedTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*</dc:subject>\\s*`, 'gi')
        updatedRdfContent = updatedRdfContent.replace(tagRegex, '\n')

        // Also handle self-closing format if it exists: <dc:subject tag="value"/>
        const selfClosingRegex = new RegExp(`\\s*<dc:subject[^>]*tag\\s*=\\s*["']\\s*${escapedTag.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*["'][^>]*/>\\s*`, 'gi')
        updatedRdfContent = updatedRdfContent.replace(selfClosingRegex, '')
      }
    })
  }

  // Process each result to add active tags as dc:subject elements
  props.results.forEach(result => {
    const item = props.allIndexedBiblioItems.find(item => item.index === result.index)
    if (!item) return

    // Get the item's title and abstract for matching
    const itemTitle = (item.title || '').trim()
    const itemAbstract = (item.abstract || item.abstractNote || '').trim()

    if (!itemTitle) return

    // Find the RDF item entry that matches this bibliographic item
    // We'll look for the item by finding its title and optionally abstract
    let itemMatch = null
    let itemStartIndex = -1
    let itemEndIndex = -1

    // Use regex to find all RDF item entries (Book, Article, etc.)
    const itemRegex = /<(bib:\w+)\s+rdf:about="[^"]*"[^>]*>([\s\S]*?)<\/\1>/g
    let match

    while ((match = itemRegex.exec(updatedRdfContent)) !== null) {
      const itemContent = match[2]

      // Check if this item contains our title
      const titleMatch = itemContent.match(/<dc:title[^>]*>([\s\S]*?)<\/dc:title>/i)
      if (titleMatch) {
        const rdfTitle = titleMatch[1].trim()

        // Match by title (exact match)
        if (rdfTitle === itemTitle) {
          // If we have an abstract, also verify it matches (for better accuracy)
          if (itemAbstract) {
            const abstractMatch = itemContent.match(/<dcterms:abstract[^>]*>([\s\S]*?)<\/dcterms:abstract>/i)
            if (abstractMatch) {
              const rdfAbstract = abstractMatch[1].trim()
              // Check if abstracts match (allowing for truncation with "...")
              if (rdfAbstract === itemAbstract ||
                  (rdfAbstract.endsWith('...') && itemAbstract.startsWith(rdfAbstract.slice(0, -3))) ||
                  (itemAbstract.endsWith('...') && rdfAbstract.startsWith(itemAbstract.slice(0, -3)))) {
                itemMatch = match
                itemStartIndex = match.index
                itemEndIndex = match.index + match[0].length
                break
              }
            }
          } else {
            // No abstract to verify, title match is sufficient
            itemMatch = match
            itemStartIndex = match.index
            itemEndIndex = match.index + match[0].length
            break
          }
        }
      }
    }

    if (!itemMatch) {
      console.warn(`Could not find RDF entry for item: ${itemTitle}`)
      return
    }

    // Get all active tags for this item
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = props.allTagCandidates.find(t => t.name === tagText)
        let decoratedTagText = tagText

        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`
        }

        return decoratedTagText
      })

    const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`
          }
        }

        const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`
        }

        return decoratedNewTagText
      })

    // Combine all tags
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]

    // Add other tag categories if selected
    if (includeConceptTagsRDF.value) allDecoratedTags.push(...result.tags.concept_tags)
    if (includePersonOrgTagsRDF.value) allDecoratedTags.push(...result.tags.person_org_tags)
    if (includeTimePlaceTagsRDF.value) allDecoratedTags.push(...result.tags.time_place_tags)

    // Add processed tag if enabled
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value)
    }

    if (allDecoratedTags.length > 0) {
      // Create dc:subject elements for each tag
      const subjectElements = allDecoratedTags.map(tag => {
        const cleanTag = String(tag).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;')
        return `        <dc:subject>${cleanTag}</dc:subject>`
      }).join('\n')

      // Find the complete </bib:authors> closing tag within this specific item
      // Search for the authors block and find its proper end
      const authorsBlockRegex = /<bib:authors[^>]*>([\s\S]*?)<\/bib:authors>/i
      const authorsMatch = itemMatch[0].match(authorsBlockRegex)

      if (authorsMatch) {
        // Find the position of this authors block within the full document
        const authorsBlockStart = updatedRdfContent.indexOf(authorsMatch[0], itemStartIndex)
        if (authorsBlockStart !== -1) {
          const authorsBlockEnd = authorsBlockStart + authorsMatch[0].length

          // Insert the new dc:subject elements after the complete </bib:authors> block
          updatedRdfContent = updatedRdfContent.substring(0, authorsBlockEnd) +
                             '\n' + subjectElements +
                             updatedRdfContent.substring(authorsBlockEnd)
        } else {
          console.warn('Could not find authors block position for item:', itemTitle)
        }
      } else {
        // Fallback: insert before the closing tag of the item
        const closingTagIndex = itemEndIndex - itemMatch[1].length - 3 // Account for "</" and ">"
        updatedRdfContent = updatedRdfContent.substring(0, closingTagIndex) +
                           '\n' + subjectElements +
                           updatedRdfContent.substring(closingTagIndex)
      }
    }
  })

  // Download the updated file
  const blob = new Blob([updatedRdfContent], { type: 'application/rdf+xml;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'updated_zotero_export.rdf'
  link.click()
}

// Download RDF function
const downloadRDF = () => {
  // If the user directly clicked the export button, the rdfFields array would be empty. So it should be set to all available fields by default.
  if (rdfFields.value.length === 0) {
    rdfFields.value = [...availableFields.value]
  }

  // Check if we should update the original file instead of creating a new one
  if (updateOriginalZoteroFile.value && props.rdfData && props.rdfData.isZoteroRdf) {
    updateOriginalRDFFile()
    return
  }

  const { tagDelimiter } = rdfOptions.value

  // Create RDF/XML header
  const rdfHeader = `<?xml version="1.0" encoding="UTF-8"?>
<rdf:RDF
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:dcterms="http://purl.org/dc/terms/"
    xmlns:prism="http://prismstandard.org/namespaces/1.2/basic/"
    xmlns:bib="http://purl.org/net/biblio#"
    xmlns:z="http://www.zotero.org/namespaces/export#"
    xmlns:foaf="http://xmlns.com/foaf/0.1/">
`

  const rdfEntries = props.results.map(result => {
    const item = props.allIndexedBiblioItems.find(item => item.index === result.index)

    // Generate a unique resource URI for this item
    const resourceId = `item_${result.index}_${Date.now()}`

    // Determine the RDF type based on item type
    const itemType = item.itemType || item.itemtype || 'article'
    let rdfType = 'bib:Article' // default

    switch (itemType.toLowerCase()) {
      case 'book':
        rdfType = 'bib:Book'
        break
      case 'journalarticle':
      case 'article':
        rdfType = 'bib:Article'
        break
      case 'conferencepaper':
        rdfType = 'bib:Article' // Use Article for conference papers
        break
      case 'thesis':
        rdfType = 'bib:Thesis'
        break
      default:
        rdfType = 'bib:Article'
    }

    let rdfEntry = `    <${rdfType} rdf:about="#${resourceId}">\n`

    // Map common fields to Dublin Core elements
    rdfFields.value.forEach(field => {
      const value = item[field]
      if (value && String(value).trim()) {
        const cleanValue = String(value).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;')

        switch (field.toLowerCase()) {
          case 'title':
            rdfEntry += `        <dc:title>${cleanValue}</dc:title>\n`
            break
          case 'author':
          case 'creators':
            // Generate Zotero-compatible bib:authors structure
            const authors = []

            if (Array.isArray(value)) {
              // Handle array of creator objects
              value.forEach(creator => {
                if (typeof creator === 'object' && (creator.firstName || creator.lastName)) {
                  authors.push({
                    surname: creator.lastName || '',
                    givenName: creator.firstName || ''
                  })
                } else if (typeof creator === 'string' && creator.trim()) {
                  // Parse string format "Surname, Given Name" or just "Name"
                  const creatorStr = String(creator).trim()
                  if (creatorStr.includes(',')) {
                    const [surname, givenName] = creatorStr.split(',').map(s => s.trim())
                    authors.push({ surname: surname || '', givenName: givenName || '' })
                  } else {
                    // Single name - treat as surname
                    authors.push({ surname: creatorStr, givenName: '' })
                  }
                }
              })
            } else if (typeof value === 'string' && value.trim()) {
              // Handle string with multiple authors separated by semicolons
              const authorStrings = String(value).split(';').map(s => s.trim()).filter(Boolean)
              authorStrings.forEach(authorStr => {
                if (authorStr.includes(',')) {
                  const [surname, givenName] = authorStr.split(',').map(s => s.trim())
                  authors.push({ surname: surname || '', givenName: givenName || '' })
                } else {
                  // Single name - treat as surname
                  authors.push({ surname: authorStr, givenName: '' })
                }
              })
            }

            // Generate the bib:authors XML structure
            if (authors.length > 0) {
              rdfEntry += `        <bib:authors>\n`
              rdfEntry += `            <rdf:Seq>\n`
              authors.forEach(author => {
                rdfEntry += `                <rdf:li>\n`
                rdfEntry += `                    <foaf:Person>\n`
                if (author.surname) {
                  rdfEntry += `                        <foaf:surname>${author.surname.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;')}</foaf:surname>\n`
                }
                if (author.givenName) {
                  rdfEntry += `                        <foaf:givenName>${author.givenName.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;')}</foaf:givenName>\n`
                }
                rdfEntry += `                    </foaf:Person>\n`
                rdfEntry += `                </rdf:li>\n`
              })
              rdfEntry += `            </rdf:Seq>\n`
              rdfEntry += `        </bib:authors>\n`
            }
            break
          case 'abstract':
          case 'abstractnote':
            rdfEntry += `        <dcterms:abstract>${cleanValue}</dcterms:abstract>\n`
            break
          case 'date':
          case 'publicationyear':
            rdfEntry += `        <dc:date>${cleanValue}</dc:date>\n`
            break
          case 'publicationtitle':
          case 'journal':
            rdfEntry += `        <dc:source>${cleanValue}</dc:source>\n`
            break
          case 'publisher':
            rdfEntry += `        <dc:publisher>${cleanValue}</dc:publisher>\n`
            break
          case 'isbn':
            rdfEntry += `        <dc:identifier>ISBN:${cleanValue}</dc:identifier>\n`
            break
          case 'issn':
            rdfEntry += `        <dc:identifier>ISSN:${cleanValue}</dc:identifier>\n`
            break
          case 'url':
            rdfEntry += `        <dc:identifier>${cleanValue}</dc:identifier>\n`
            break
          case 'itemtype':
            rdfEntry += `        <z:itemType>${cleanValue}</z:itemType>\n`
            break
          case 'language':
            rdfEntry += `        <dc:language>${cleanValue}</dc:language>\n`
            break
          case 'volume':
            rdfEntry += `        <prism:volume>${cleanValue}</prism:volume>\n`
            break
          case 'issue':
            rdfEntry += `        <prism:number>${cleanValue}</prism:number>\n`
            break
          case 'pages':
            rdfEntry += `        <prism:startingPage>${cleanValue}</prism:startingPage>\n`
            break
          default:
            // For other fields, use a generic Dublin Core relation
            rdfEntry += `        <dc:relation rdf:parseType="Resource">\n`
            rdfEntry += `            <dc:description>${field}: ${cleanValue}</dc:description>\n`
            rdfEntry += `        </dc:relation>\n`
        }
      }
    })

    // Process active tags and add to keywords field (same logic as BibTeX and RIS)
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = props.allTagCandidates.find(t => t.name === tagText)

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = tagText
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`
        }

        return decoratedTagText
      })

    // Process newly added customized tags
    const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`
          }
        }

        const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`
        }

        return decoratedNewTagText
      })

    // Combine all tags
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]

    // Add other tag categories if selected
    if (includeConceptTagsRDF.value) allDecoratedTags.push(...result.tags.concept_tags)
    if (includePersonOrgTagsRDF.value) allDecoratedTags.push(...result.tags.person_org_tags)
    if (includeTimePlaceTagsRDF.value) allDecoratedTags.push(...result.tags.time_place_tags)

    // Add processed tag if enabled
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value)
    }

    // Add tags as subjects - each tag gets its own dc:subject element for better RDF structure
    if (allDecoratedTags.length > 0) {
      allDecoratedTags.forEach(tag => {
        const cleanTag = String(tag).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;')
        rdfEntry += `        <dc:subject>${cleanTag}</dc:subject>\n`
      })
    }

    rdfEntry += `    </${rdfType}>\n`

    return rdfEntry
  })

  // Create the complete RDF content
  const rdfFooter = `</rdf:RDF>`
  const rdfContent = rdfHeader + rdfEntries.join('\n') + rdfFooter

  // Download the file
  const blob = new Blob([rdfContent], { type: 'application/rdf+xml;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'tagged_items.rdf'
  link.click()
}

// Save matched tags to Zotero
const saveTagsToZotero = async () => {
  if (props.currentSource !== 'Zotero' || !props.zoteroConfig) {
    ElMessage.error('Can only save to Zotero for items imported from Zotero')
    return
  }

  try {
    const { libraryId, apiKey } = props.zoteroConfig
    let successCount = 0
    let failureCount = 0

    const updatePromises = props.results.map(async (result) => {
      const item = props.allIndexedBiblioItems.find(item => item.index === result.index)
      if (!item?.key || !item?.version) return null

      try {
        // Fetch the current item to get its existing tags
        const currentItemResponse = await axios.get(
          `${props.zoteroBaseUrl}/items/${item.key}`,
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`
            }
          }
        )

        // Get existing tags, excluding the tag used for import and any previously added suffixes
        const existingTags = currentItemResponse.data.data.tags
          .filter(tagObj => {
            const tag = tagObj.tag
            return tag !== props.zoteroConfig.tag &&
              (!customSuffixEnabled.value || !tag.endsWith(customSuffix.value)) &&
              (!processedTagEnabled.value || tag !== processedTag.value)
          })
          .map(tagObj => tagObj.tag)

        // Process matched tags with metadata and custom suffixes
        const decoratedActiveMatchedTags = result.tags.matched_tags
          .filter(tag => !isTagDeselected(result.index, tag))
          .map(tagName => {
            let decoratedTag = tagName
            const matchingTag = props.allTagCandidates.find(t => t.name === tagName)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Process new tags with metadata and custom suffixes
        const decoratedNewTags = Array.from(props.newTags.get(result.index) || [])
          .map(tag => {
            let tagText = tag.text
            // Add new tag mark if enabled
            if (newTagMarkEnabled.value && !tag.isMatched) {
              if (newTagMarkPosition.value === 'prefix') {
                tagText = `${newTagMark.value}${tagText}`
              } else if (newTagMarkPosition.value === 'suffix') {
                tagText = `${tagText}${newTagMark.value}`
              }
            }

            let decoratedTag = tagText
            const matchingTag = props.allTagCandidates.find(t => t.name === tag.text)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Combine all tags
        const allDecoratedTags = [...new Set([
          ...existingTags,
          ...decoratedActiveMatchedTags,
          ...decoratedNewTags
        ])]

        // Format tags for Zotero API
        const tagsToUpdate = allDecoratedTags.map(tag => ({ tag }))

        // Add the processed tag if enabled
        if (processedTagEnabled.value && processedTag.value) {
          tagsToUpdate.push({ tag: processedTag.value })
        }

        // Update the item with combined tags
        await axios.patch(
          `${props.zoteroBaseUrl}/items/${item.key}`,
          {
            tags: tagsToUpdate
          },
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'If-Unmodified-Since-Version': currentItemResponse.data.version
            }
          }
        )
        successCount++
      } catch (error) {
        console.error(`Error updating item ${item.key}:`, error)
        failureCount++
        throw error
      }
    })

    await Promise.all(updatePromises)

    if (failureCount === 0) {
      ElMessage.success(`Successfully saved tags to Zotero for all ${successCount} items`)
    } else {
      ElMessage.warning(`Saved tags for ${successCount} items, failed for ${failureCount} items`)
    }
  } catch (error) {
    console.error('Error saving tags to Zotero:', error)
    let errorMessage = 'Failed to save tags to Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library or item not found'
          break
        case 412:
          errorMessage = 'Item was modified since last retrieval. Please refresh and try again'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  }
}

// New consolidated event handlers
const handleDownload = () => {
  switch (selectedExportFormat.value) {
    case 'csv':
      downloadCSV()
      break
    case 'bibtex':
      downloadBibTeX()
      break
    case 'ris':
      downloadRIS()
      break
    case 'rdf':
      downloadRDF()
      break
  }
}

const handleOpenOptionsDialog = () => {
  switch (selectedExportFormat.value) {
    case 'csv':
      openExportDialog()
      break
    case 'bibtex':
      openBibTeXDialog()
      break
    case 'ris':
      openRISDialog()
      break
    case 'rdf':
      openRDFDialog()
      break
  }
}

// Legacy event handlers (kept for compatibility with existing dialogs)
const handleDownloadCSV = () => {
  downloadCSV()
}

const handleOpenExportDialog = () => {
  openExportDialog()
}

const handleDownloadBibTeX = () => {
  downloadBibTeX()
}

const handleOpenBibTeXDialog = () => {
  openBibTeXDialog()
}

const handleDownloadRIS = () => {
  downloadRIS()
}

const handleOpenRISDialog = () => {
  openRISDialog()
}

const handleDownloadRDF = () => {
  downloadRDF()
}

const handleOpenRDFDialog = () => {
  openRDFDialog()
}

const handleSaveTagsToZotero = () => {
  saveTagsToZotero()
}

// Watchers
watch(() => props.allTagCandidates, updateMetadataFields, { immediate: true })

watch(metadataSuffixEnabled, async (newValue) => {
  if (newValue === true) {
    emit('fetchAllTags')
  }
})
</script>

<style scoped>
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.mark-position-select {
  width: 80px;
  margin: 0 5px;
}

.suffix-input {
  margin-top: 10px;
}

.preview-tag {
  margin: 2px;
}

.mt-4 {
  margin-top: 20px;
}

.el-input-delimiter {
  width: 60px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
